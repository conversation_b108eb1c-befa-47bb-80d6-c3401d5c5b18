package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.didi.service.DidiProjectSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 滴滴项目同步控制器
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@RestController
@RequestMapping("/didi/project")
@Api(tags = "滴滴项目同步管理")
@AllArgsConstructor
public class DidiProjectSyncController {

    private final DidiProjectSyncService didiProjectSyncService;



    @PostMapping("/project/batch-sync")
    @ApiOperation("批量同步项目信息至滴滴")
    public ApiResult<String> batchSyncProjects() {
        didiProjectSyncService.batchSyncProjects();
        return ApiResult.successMsg("批量同步项目信息成功");
    }


} 