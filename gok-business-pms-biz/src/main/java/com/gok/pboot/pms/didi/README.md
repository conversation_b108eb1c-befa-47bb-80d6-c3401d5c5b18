# 滴滴企业版项目同步功能

## 功能概述

本模块实现了将项目管理系统中的项目信息同步至滴滴企业版的功能，支持单个项目同步、批量同步、更新和删除操作。

## 主要功能

### 1. 项目信息同步
- 同步单个项目信息至滴滴
- 批量同步多个项目信息
- 更新已同步的项目信息
- 删除滴滴中的项目信息

### 2. 数据映射
- 项目基本信息映射（编号、名称、状态等）
- 时间信息映射（开始时间、结束时间）
- 预算信息映射（金额转换为分）
- 负责人信息映射
- 客户信息映射
- 成本中心信息映射

## 文件结构

```
didi/
├── client/
│   └── DidiClient.java              # 滴滴API客户端接口
├── dto/
│   ├── DidiAuthReq.java             # 认证请求DTO
│   ├── DidiAuthRes.java             # 认证响应DTO
│   ├── DidiProjectSyncReq.java      # 项目同步请求DTO
│   ├── DidiProjectSyncRes.java      # 项目同步响应DTO
│   └── DidiResult.java              # 通用响应DTO
├── service/
│   ├── DidiProjectSyncService.java  # 同步服务接口
│   └── impl/
│       └── DidiProjectSyncServiceImpl.java  # 同步服务实现
├── util/
│   └── DidiSignUtil.java            # 签名工具类
└── README.md                        # 说明文档
```

## 配置说明

### 1. 配置文件
在 `application.yml` 或 `application-didi.yml` 中配置：

```yaml
didi:
  client:
    id: ${DIDI_CLIENT_ID:your_client_id}
    secret: ${DIDI_CLIENT_SECRET:your_client_secret}
    grant-type: client_credentials
  
  api:
    base-url: https://api.es.xiaojukeji.com
    timeout: 30000
    retry-count: 3
  
  sync:
    enabled: false
    interval: 30
    batch-size: 100
    async: true
```

### 2. 环境变量
- `DIDI_CLIENT_ID`: 滴滴企业版客户端ID
- `DIDI_CLIENT_SECRET`: 滴滴企业版客户端密钥

## API接口

### 1. 同步单个项目
```http
POST /didi/project/sync
Content-Type: application/json

{
  "itemNo": "PRJ001",
  "itemName": "示例项目",
  "projectStatus": "1",
  "projectDate": "2024-01-01",
  "expectedCompleteTime": "2024-12-31",
  "proPackageBudget": 100000.00,
  "managerUserName": "张三",
  "customerName": "客户公司",
  "projectLocation": "北京市朝阳区"
}
```

### 2. 更新项目信息
```http
POST /didi/project/update
Content-Type: application/json

{
  "itemNo": "PRJ001",
  "itemName": "更新后的项目名称",
  "projectStatus": "2"
}
```

### 3. 删除项目
```http
DELETE /didi/project/delete/PRJ001
```

### 4. 批量同步项目
```http
POST /didi/project/batch-sync
Content-Type: application/json

[
  {
    "itemNo": "PRJ001",
    "itemName": "项目1"
  },
  {
    "itemNo": "PRJ002", 
    "itemName": "项目2"
  }
]
```

### 5. 查询同步状态
```http
GET /didi/project/sync-status/PRJ001
```

## 使用示例

### 1. 注入服务
```java
@Autowired
private DidiProjectSyncService didiProjectSyncService;
```

### 2. 同步单个项目
```java
ProjectInfo projectInfo = new ProjectInfo();
projectInfo.setItemNo("PRJ001");
projectInfo.setItemName("示例项目");
projectInfo.setProjectStatus("1");

boolean success = didiProjectSyncService.syncProjectToDidi(projectInfo);
if (success) {
    System.out.println("项目同步成功");
} else {
    System.out.println("项目同步失败");
}
```

### 3. 批量同步
```java
List<ProjectInfo> projectList = getProjectList();
boolean success = didiProjectSyncService.batchSyncProjects(projectList);
```

## 注意事项

### 1. 签名算法
- 当前实现的签名算法为MD5，可能需要根据滴滴官方文档调整
- 签名参数顺序需要按照滴滴要求进行排序

### 2. 数据转换
- 金额需要转换为分（乘以100）
- 时间格式需要转换为 `yyyy-MM-dd`
- 项目状态需要映射为滴滴的状态码

### 3. 错误处理
- 所有API调用都有异常处理
- 失败时会记录详细日志
- 支持重试机制

### 4. 性能优化
- 支持批量同步减少API调用次数
- 支持异步处理提高响应速度
- 可配置超时时间和重试次数

## 扩展功能

### 1. 自动同步
可以添加定时任务，定期同步项目信息：

```java
@Scheduled(fixedRate = 1800000) // 30分钟
public void autoSyncProjects() {
    List<ProjectInfo> projects = getUnsyncedProjects();
    didiProjectSyncService.batchSyncProjects(projects);
}
```

### 2. 同步状态跟踪
可以添加数据库表记录同步状态：

```sql
CREATE TABLE didi_sync_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_code VARCHAR(50),
    sync_status TINYINT,
    sync_time DATETIME,
    error_message TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 回调处理
可以添加滴滴回调接口处理同步结果：

```java
@PostMapping("/didi/callback")
public ApiResult<String> handleCallback(@RequestBody String callbackData) {
    // 处理滴滴回调
    return ApiResult.success("处理成功");
}
```

## 常见问题

### 1. 认证失败
- 检查客户端ID和密钥是否正确
- 确认签名算法是否正确
- 检查时间戳是否在有效期内

### 2. 同步失败
- 检查项目数据是否完整
- 确认必填字段是否已填写
- 查看错误日志获取详细信息

### 3. 网络问题
- 检查网络连接是否正常
- 调整超时时间配置
- 启用重试机制

## 更新日志

- 2025-01-27: 初始版本，实现基本的项目同步功能
- 支持单个和批量同步
- 支持项目更新和删除
- 添加完整的错误处理和日志记录 