package com.gok.pboot.pms.didi.util;

import com.gok.pboot.pms.didi.dto.DidiAuthReq;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

/**
 * 滴滴签名工具类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
public class DidiSignUtil {

    /**
     * 生成签名
     * 根据滴滴文档的签名算法实现
     *
     * @param authReq 认证请求
     * @return 签名
     */
    public static String generateSign(DidiAuthReq authReq) {
        try {
            // 构建参数字符串
            TreeMap<String, String> params = new TreeMap<>();
            params.put("client_id", authReq.getClient_id());
            params.put("client_secret", authReq.getClient_secret());
            params.put("grant_type", authReq.getGrant_type());
            params.put("timestamp", String.valueOf(authReq.getTimestamp()));

            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (signStr.length() > 0) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
            }

            // 使用MD5加密
            return md5(signStr.toString());
        } catch (Exception e) {
            log.error("生成签名异常", e);
            return null;
        }
    }

    /**
     * 生成API调用签名
     *
     * @param params 参数
     * @param secret 密钥
     * @return 签名
     */
    public static String generateApiSign(Map<String, String> params, String secret) {
        try {
            // 按参数名排序
            TreeMap<String, String> sortedParams = new TreeMap<>(params);
            
            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
                if (signStr.length() > 0) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            
            // 添加密钥
            signStr.append("&key=").append(secret);
            
            // 使用MD5加密并转为大写
            return md5(signStr.toString()).toUpperCase();
        } catch (Exception e) {
            log.error("生成API签名异常", e);
            return null;
        }
    }

    /**
     * MD5加密
     *
     * @param input 输入字符串
     * @return MD5哈希值
     */
    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密异常", e);
            return null;
        }
    }

    /**
     * SHA256加密
     *
     * @param input 输入字符串
     * @return SHA256哈希值
     */
    public static String sha256(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA256加密异常", e);
            return null;
        }
    }
} 