package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.base.StatisticsPage;
import com.gok.pboot.pms.entity.DdCommonOrder;
import com.gok.pboot.pms.entity.DdFlightTicketOrder;
import com.gok.pboot.pms.entity.DdHotelOrder;
import com.gok.pboot.pms.entity.DdVehicleOrder;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.DdFlightTicketOrderImportDTO;
import com.gok.pboot.pms.entity.dto.DdHotelOrderImportDTO;
import com.gok.pboot.pms.entity.dto.DdOrderQueryDTO;
import com.gok.pboot.pms.entity.dto.DdVehicleOrderImportDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.BusinessOrderReimbursedStatusEnum;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import com.gok.pboot.pms.enumeration.OrderTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.handler.BcpDataHandler;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.DdOrderMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.*;
import com.gok.pboot.pms.service.IDdOrderService;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.RosterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 滴滴订单管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DdOrderServiceImpl implements IDdOrderService {
    private final IProjectInfoService projectInfoService;
    private final DdOrderMapper ddOrderMapper;
    private final DbApiUtil dbApiUtil;
    private final OaUtil oaUtil;
    private final OaClient oaClient;
    private final BcpDataHandler bcpDataHandler;
    private final RosterService rosterService;
    private final ProjectScopeHandle projectScopeHandle;
    @Resource
    private RemoteBcpDictService remoteBcpDictService;

    @Value("${oa.url.httpUrl}")
    private String url;

    @Value("${oa.resourcesAppId}")
    private String appId;

    @Value("${oa.spk}")
    private String spk;

    @Override
    public StatisticsPage<DdFlightTicketOrderFindPageVO> findFlightTicketPageList(StatisticsPage<DdFlightTicketOrderFindPageVO> page, Map<String, Object> queryDTO) {

        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope, OrderTypeEnum.FLIGHT_TICKET);
        }
        page.getRecords().forEach(item ->
                item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus())));
        ddOrderMapper.findFlightTicketPageList(page, queryDTO);
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.flightTicketOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    @Override
    public StatisticsPage<DdHotelOrderFindPageVO> findHotelPageList(StatisticsPage<DdHotelOrderFindPageVO> page, Map<String, Object> queryDTO) {
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope,OrderTypeEnum.HOTEL);
        }
        ddOrderMapper.findHotelPageList(page, queryDTO);
        page.getRecords().forEach(item ->
                item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus())));
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.hotelOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    @Override
    public StatisticsPage<DdVehicleOrderFindPageVO> findVehiclePageList(StatisticsPage<DdVehicleOrderFindPageVO> page, Map<String, Object> queryDTO) {
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope, OrderTypeEnum.VEHICLE);
        }
        page.getRecords().forEach(item ->
                item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus())));
        ddOrderMapper.findVehiclePageList(page, queryDTO);
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.vehicleOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    private void scopeHandle(Map<String, Object> queryDTO,
                             SysUserDataScopeVO dataScope,
                             OrderTypeEnum orderTypeEnum) {
        queryDTO.put("scope",true);
        queryDTO.put("orderType", orderTypeEnum.getCode());
        queryDTO.put("userIdList", dataScope.getUserIdList());
        List<Long> deptIdList = dataScope.getDeptIdList();
        if (CollUtil.isEmpty(deptIdList)) {
            return;
        }
        // 获取合同主体维度架构数据
        List<MultiDimensionDeptDto> multiDeptList = bcpDataHandler.getMultiDeptList("合同主体");
        Map<Long, String> multiDeptMap = CollStreamUtil.toMap(multiDeptList, MultiDimensionDeptDto::getDeptId, MultiDimensionDeptDto::getName);
        Set<String> subjectNames = new HashSet<>();
        deptIdList.forEach(deptId -> {
            String subject = multiDeptMap.get(deptId);
            if (subject != null) {
                subjectNames.add(subject);
            }
        });
        queryDTO.put("subjectNames", subjectNames);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DdOrderImportResultVO importOrders(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        try {
            // 读取机票订单数据
            List<DdFlightTicketOrderImportDTO> flightTicketOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdFlightTicketOrderImportDTO.class, new AnalysisEventListener<DdFlightTicketOrderImportDTO>() {
                @Override
                public void invoke(DdFlightTicketOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        flightTicketOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理机票订单数据
                    processFlightTicketOrders(flightTicketOrders, errorMessages, successCount, failCount);
                }
            }).headRowNumber(2).sheet("机票订单").doRead();

            // 读取酒店订单数据
            List<DdHotelOrderImportDTO> hotelOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdHotelOrderImportDTO.class, new AnalysisEventListener<DdHotelOrderImportDTO>() {
                @Override
                public void invoke(DdHotelOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        hotelOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理酒店订单数据
                    processHotelOrders(hotelOrders, errorMessages, successCount, failCount);
                }
            }).headRowNumber(2).sheet("酒店订单").doRead();

            // 读取用车订单数据
            List<DdVehicleOrderImportDTO> vehicleOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdVehicleOrderImportDTO.class, new AnalysisEventListener<DdVehicleOrderImportDTO>() {
                @Override
                public void invoke(DdVehicleOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        vehicleOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理用车订单数据
                    processVehicleOrders(vehicleOrders, errorMessages, successCount, failCount);
                }
            }).headRowNumber(2).sheet("用车订单").doRead();

        } catch (IOException e) {
            log.error("导入订单数据失败", e);
            errorMessages.add("文件读取失败：" + e.getMessage());
            failCount.incrementAndGet();
        }

        return new DdOrderImportResultVO(successCount.get(), failCount.get(), errorMessages);
    }

    @Override
    @Async
    public void generateReimbursement(DdOrderQueryDTO queryDTO, PigxUser user) {
        try {
            log.info("开始生成报销单，查询条件：{}", queryDTO);
            // 获取OA用户信息
            OaAccountVO oaAccountVO = dbApiUtil.getOaAccountInfoByUserId(user.getId());
            if (oaAccountVO == null || oaAccountVO.getOaId() == null) {
                log.error("获取OA用户信息失败，用户ID：{}", user.getId());
                return;
            }

            // 获取所有类型的可发起报销订单
            List<DdCommonOrder> allOrders = getAllCanInitiateOrders(queryDTO);

            if (allOrders.isEmpty()) {
                log.warn("没有找到可发起报销的订单");
                return;
            }

            log.info("找到可发起报销的订单数量：{}", allOrders.size());

            //获取OA部门月预算
            Map<Long, List<OaBmyysbDTO>> bmyysbMap = dbApiUtil.getInfoByBmyysb().stream().collect(Collectors.groupingBy(OaBmyysbDTO::getYsbm));

            //获取OA部门
            Map<Long, OaDeptDTO> deptMap = dbApiUtil.getInfoByDept().stream().collect(Collectors.toMap(OaDeptDTO::getDeptId, v -> v));

            // 获取OA项目台帐
            List<Long> projectIds = allOrders.stream().map(DdCommonOrder::getProjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, ProjectAccountVO> projectAccountMap = dbApiUtil.getProjectAccountByName(null, projectIds).stream().collect(Collectors.toMap(ProjectAccountVO::getId, v -> v));

            //获取OA预算台账
            Map<Long, List<OaXmysbDTO>> xmysbMap = dbApiUtil.getInfoByXmysb(null, projectIds).stream().collect(Collectors.groupingBy(OaXmysbDTO::getXmmc));

            // 根据业务规则生成报销单
            Map<String, List<DdCommonOrder>> groupedOrders = groupOrdersByReimbursementRule(allOrders, bmyysbMap, deptMap);

            // 用于批量更新的集合
            List<DdCommonOrder> successOrders = new ArrayList<>(); // 成功发起的订单
            List<DdCommonOrder> budgetErrorOrders = new ArrayList<>(); // 预算不足的订单
            List<DdCommonOrder> oaErrorOrders = new ArrayList<>(); // OA接口错误的订单
            Map<Long, Long> orderRequestIdMap = new HashMap<>(); // 订单ID -> 请求ID映射

            // 调用OA接口生成报销单
            for (Map.Entry<String, List<DdCommonOrder>> entry : groupedOrders.entrySet()) {
                String groupKey = entry.getKey();
                List<DdCommonOrder> orderGroup = entry.getValue();

                log.info("生成报销单，分组键：{}，订单数量：{}", groupKey, orderGroup.size());

                try {
                    // 解析分组键，获取OA表单类型
                    String[] keyParts = groupKey.split("_");
                    Integer oaFormType = Integer.valueOf(keyParts[0]);
                    Long companyId = Long.valueOf(keyParts[1]);
                    Long deptProjectId = Long.valueOf(keyParts[2]);

                    // 调用OA接口创建报销单
                    Long requestId = callOACreateReimbursement(oaAccountVO, oaFormType, orderGroup, companyId, deptProjectId, projectAccountMap, xmysbMap, bmyysbMap);

                    if (requestId != null) {
                        log.info("成功创建OA报销单，请求ID：{}，分组键：{}", requestId, groupKey);
                        // 记录订单与请求ID的映射关系
                        for (DdCommonOrder order : orderGroup) {
                            orderRequestIdMap.put(order.getId(), requestId);
                        }
                        // 收集成功的订单
                        successOrders.addAll(orderGroup);
                    } else {
                        log.error("创建OA报销单失败，分组键：{}", groupKey);
                        // 检查是否是预算不足错误
                        boolean isBudgetError = orderGroup.stream()
                                .anyMatch(order -> BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue()
                                        .equals(order.getInitiationStatus()));
                        if (isBudgetError) {
                            budgetErrorOrders.addAll(orderGroup);
                        } else {
                            oaErrorOrders.addAll(orderGroup);
                        }
                    }
                } catch (Exception e) {
                    log.error("创建OA报销单失败，分组键：{}，错误：{}", groupKey, e.getMessage(), e);
                    // 检查是否已经是预算不足错误
                    boolean isBudgetError = orderGroup.stream()
                            .anyMatch(order -> BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue()
                                    .equals(order.getInitiationStatus()));
                    if (isBudgetError) {
                        budgetErrorOrders.addAll(orderGroup);
                    } else {
                        oaErrorOrders.addAll(orderGroup);
                    }
                }
            }

            // 批量更新订单状态
            batchUpdateOrderStatus(successOrders, budgetErrorOrders, oaErrorOrders, orderRequestIdMap);

            log.info("报销单生成完成，共生成{}个报销单，成功：{}，预算不足：{}，OA错误：{}",
                    groupedOrders.size(), successOrders.size(), budgetErrorOrders.size(), oaErrorOrders.size());

        } catch (Exception e) {
            log.error("生成报销失败", e);
        }
    }

    /**
     * 批量更新订单状态
     */
    private void batchUpdateOrderStatus(List<DdCommonOrder> successOrders,
                                        List<DdCommonOrder> budgetErrorOrders,
                                        List<DdCommonOrder> oaErrorOrders,
                                        Map<Long, Long> orderRequestIdMap) {
        try {
            // 批量更新成功发起的订单
            if (CollUtil.isNotEmpty(successOrders)) {
                // 按requestId分组，因为updateOrderReimbursementStatus方法只能处理相同requestId的订单
                Map<Long, List<DdCommonOrder>> ordersByRequestId = new HashMap<>();
                for (DdCommonOrder order : successOrders) {
                    Long requestId = orderRequestIdMap.get(order.getId());
                    order.setRequestId(requestId);
                    ordersByRequestId.computeIfAbsent(requestId, k -> new ArrayList<>()).add(order);
                }

                // 分批更新不同requestId的订单
                for (Map.Entry<Long, List<DdCommonOrder>> entry : ordersByRequestId.entrySet()) {
                    updateOrderReimbursementStatus(entry.getValue(), entry.getKey(), BusinessOrderReimbursedStatusEnum.INITIATED);
                }
                log.info("批量更新成功发起订单状态，数量：{}", successOrders.size());
            }

            // 批量更新预算不足的订单
            if (CollUtil.isNotEmpty(budgetErrorOrders)) {
                updateOrderReimbursementStatus(budgetErrorOrders, null, BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET);
                log.info("批量更新预算不足订单状态，数量：{}", budgetErrorOrders.size());
            }

            // 批量更新OA接口错误的订单
            if (CollUtil.isNotEmpty(oaErrorOrders)) {
                updateOrderReimbursementStatus(oaErrorOrders, null, BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_OA);
                log.info("批量更新OA接口错误订单状态，数量：{}", oaErrorOrders.size());
            }
        } catch (Exception e) {
            log.error("批量更新订单状态失败", e);
        }
    }

    /**
     * 获取所有类型的可发起报销订单
     */
    private List<DdCommonOrder> getAllCanInitiateOrders(DdOrderQueryDTO queryDTO) {
        List<DdCommonOrder> allOrders = new ArrayList<>();
        List<Long> selectedIds = queryDTO.getSelectedOrderIds();

        // 查询机票订单并转换为通用订单
        List<DdFlightTicketOrder> flightOrders = ddOrderMapper.findCanInitiateFlightTicketOrders(queryDTO, selectedIds);
        for (DdFlightTicketOrder order : flightOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.FLIGHT_TICKET);
            allOrders.add(commonOrder);
        }

        // 查询酒店订单并转换为通用订单
        List<DdHotelOrder> hotelOrders = ddOrderMapper.findCanInitiateHotelOrders(queryDTO, selectedIds);
        for (DdHotelOrder order : hotelOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.HOTEL);
            allOrders.add(commonOrder);
        }

        // 查询用车订单并转换为通用订单
        List<DdVehicleOrder> vehicleOrders = ddOrderMapper.findCanInitiateVehicleOrders(queryDTO, selectedIds);
        for (DdVehicleOrder order : vehicleOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.VEHICLE);
            allOrders.add(commonOrder);
        }

        return allOrders;
    }

    /**
     * 根据报销规则对订单进行分组
     */
    private Map<String, List<DdCommonOrder>> groupOrdersByReimbursementRule(List<DdCommonOrder> orders,
                                                                             Map<Long, List<OaBmyysbDTO>> bmyysbMap,
                                                                             Map<Long, OaDeptDTO> deptMap) {
        Map<String, List<DdCommonOrder>> groupedOrders = new HashMap<>();
        for (DdCommonOrder order : orders) {
            String groupKey = getReimbursementGroupKey(order, bmyysbMap, deptMap);
            if (groupKey != null) {
                groupedOrders.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(order);
            }
        }

        return groupedOrders;
    }

    /**
     * 获取报销分组键
     */
    private String getReimbursementGroupKey(DdCommonOrder order, Map<Long, List<OaBmyysbDTO>> bmyysbMap, Map<Long, OaDeptDTO> deptMap) {
        // 场景一：所属项目为空，同【所属公司】、同【预算一级部门】进行报销单合并
        if (order.getProjectId() == null && order.getCompanyId() != null && order.getCostCenterId() != null) {
            // 成本中心ID通过OA部门转成OA部门id，OA部门id通过OA部门月预算获取预算一级部门
            Long oaDeptId = deptMap.get(order.getCostCenterId()).getOaDeptId();
            if (oaDeptId != null) {
                Long budgetFirstLevelDept = getBudgetFirstLevelDeptByOaDeptId(oaDeptId, bmyysbMap);
                if (budgetFirstLevelDept != null) {
                    return OAFormTypeEnum.FYBXD.getValue() + "_" + order.getCompanyId() + "_" + budgetFirstLevelDept;
                }
            }
        }
        // 场景二：成本中心为空，同OA【所属公司】、同OA【项目名称】进行报销单合并
        else if (order.getCostCenterId() == null && order.getCompanyId() != null && order.getProjectId() != null) {
            return getProjectReimbursementType(order.getProjectStatus()) + "_" + order.getCompanyId() + "_" + order.getProjectId();
        }
        return null;
    }



    /**
     * 根据项目状态获取报销单类型
     */
    private Integer getProjectReimbursementType(Integer projectStatus) {
        // 根据项目状态判断报销单类型
        if (projectStatus != null) {
            if (ProjectStatusEnum.SJ.getValue().equals(projectStatus)) {
                // 商机项目 - XM-28商机项目费用报销
                return OAFormTypeEnum.SJFYBX.getValue();
            } else if (ProjectStatusEnum.ZJ.getValue().equals(projectStatus)) {
                // 在建项目 - XM-15在建项目费用报销
                return OAFormTypeEnum.ZJXMFYBX.getValue();
            }
        }

        return OAFormTypeEnum.ZJXMFYBX.getValue(); // 默认在建项目
    }

    /**
     * 预算校验
     */
    private String validateBudget(OAFormTypeEnum formTypeEnum, List<DdCommonOrder> orders, Long deptProjectId,
                                  Map<Long, List<OaXmysbDTO>> xmysbMap, Map<Long, List<OaBmyysbDTO>> bmyysbMap) {

        // 计算总报销金额
        BigDecimal totalAmount = orders.stream()
                .map(DdCommonOrder::getCompanyActualPayment)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (formTypeEnum == OAFormTypeEnum.FYBXD) {
            // CW-03费用报销单：使用OA部门id通过OA部门月预算校验
            return validateDepartmentBudget(deptProjectId, totalAmount, bmyysbMap);
        } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
            // XM-28商机项目费用报销：使用项目id和科目代码5601校验
            return validateProjectBudget(deptProjectId, totalAmount, "5601", xmysbMap);
        } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
            // XM-15在建项目费用报销：使用项目id和科目代码5602校验
            return validateProjectBudget(deptProjectId, totalAmount, "5602", xmysbMap);
        }

        return null; // 无需校验或校验通过
    }

    /**
     * 校验部门月预算
     */
    private String validateDepartmentBudget(Long deptId, BigDecimal totalAmount, Map<Long, List<OaBmyysbDTO>> bmyysbMap) {
        List<OaBmyysbDTO> deptBudgets = bmyysbMap.get(deptId);
        if (CollUtil.isEmpty(deptBudgets)) {
            return "未找到部门预算信息，部门ID：" + deptId;
        }

        // 获取当前年月
        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 查找当前月份的预算
        OaBmyysbDTO currentMonthBudget = deptBudgets.stream()
                .filter(budget -> currentYearMonth.equals(budget.getYsny()))
                .findFirst()
                .orElse(null);

        if (currentMonthBudget == null) {
            return "未找到当前月份的部门预算，部门ID：" + deptId + "，年月：" + currentYearMonth;
        }

        // 校验可用余额
        BigDecimal availableAmount = currentMonthBudget.getByyskyye();
        if (availableAmount == null || availableAmount.compareTo(totalAmount) < 0) {
            return String.format("部门预算不足，可用余额：%s，申请金额：%s",
                    availableAmount != null ? availableAmount.toString() : "0", totalAmount.toString());
        }

        return null; // 校验通过
    }

    /**
     * 校验项目预算
     */
    private String validateProjectBudget(Long projectId, BigDecimal totalAmount, String accountCode,
                                         Map<Long, List<OaXmysbDTO>> xmysbMap) {
        List<OaXmysbDTO> projectBudgets = xmysbMap.get(projectId);
        if (CollUtil.isEmpty(projectBudgets)) {
            return "未找到项目预算信息，项目ID：" + projectId;
        }

        // 查找对应科目代码的预算
        OaXmysbDTO matchedBudget = projectBudgets.stream()
                .filter(budget -> accountCode.equals(budget.getKmdm()))
                .findFirst()
                .orElse(null);

        if (matchedBudget == null) {
            return "未找到对应科目的项目预算，项目ID：" + projectId + "，科目代码：" + accountCode;
        }

        // 校验可用预算金额
        BigDecimal availableAmount = matchedBudget.getKyysje();
        if (availableAmount == null || availableAmount.compareTo(totalAmount) < 0) {
            return String.format("项目预算不足，可用预算金额：%s，申请金额：%s",
                    availableAmount != null ? availableAmount.toString() : "0", totalAmount.toString());
        }

        return null; // 校验通过
    }

    /**
     * 通过OA部门ID获取预算一级部门
     */
    private Long getBudgetFirstLevelDeptByOaDeptId(Long oaDeptId, Map<Long, List<OaBmyysbDTO>> bmyysbMap) {
        List<OaBmyysbDTO> deptBudgets = bmyysbMap.get(oaDeptId);
        if (CollUtil.isEmpty(deptBudgets)) {
            log.warn("未找到部门预算信息，部门ID：{}", oaDeptId);
            return null;
        }

        // 获取当前年月
        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 查找当前月份的预算，获取预算一级部门
        OaBmyysbDTO currentMonthBudget = deptBudgets.stream()
                .filter(budget -> currentYearMonth.equals(budget.getYsny()))
                .findFirst()
                .orElse(deptBudgets.get(0)); // 如果没找到当前月份，取第一个

        return currentMonthBudget.getYsyjbm();
    }


    /**
     * 处理机票订单数据
     */
    private void processFlightTicketOrders(List<DdFlightTicketOrderImportDTO> orders,
                                           List<String> errorMessages,
                                           AtomicInteger successCount,
                                           AtomicInteger failCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdFlightTicketOrder> validEntities = new ArrayList<>();
        List<DdFlightTicketOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            orderNoSet.add(order.getOrderNo());
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getPassengerEmployeeNo())) {
                workCodeSet.add(order.getPassengerEmployeeNo());
            }
        });
        // 查询已存在的酒店订单
        Map<String,DdFlightTicketOrder> flightTicketOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getFlightTicketOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdFlightTicketOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));

        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdFlightTicketOrderImportDTO order : orders) {
            try {
                // 数据校验
                String errorMsg = validateFlightTicketOrder(order, projectInfoMap, deptIdMapByName, rosterMap,companyMap);

                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("机票订单[" + order.getOrderNo() + "]：" + errorMsg);
                    failCount.incrementAndGet();
                    continue;
                }
                // 转换为实体
                DdFlightTicketOrder entity = convertToFlightTicketOrder(order);

                if (flightTicketOrderMap.containsKey(order.getOrderNo())) {
                    DdFlightTicketOrder flightTicketOrder = flightTicketOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(flightTicketOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("机票订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(entity);
                } else {
                    validEntities.add(entity);
                }

            } catch (Exception e) {
                log.error("处理机票订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("机票订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
                failCount.incrementAndGet();
            }
        }

        // 批量插入有效数据
        if (!validEntities.isEmpty()) {
            try {
                int insertCount = ddOrderMapper.batchInsertFlightTicketOrders(validEntities);
                int updateCount = ddOrderMapper.batchUpdateFlightTicketOrder(updateEntities);
                successCount.addAndGet(insertCount + updateCount);
            } catch (Exception e) {
                log.error("批量新增/更新机票订单失败", e);
                invalidOrders.add("机票订单批量处理失败，请联系管理员");
                failCount.addAndGet(validEntities.size() + updateEntities.size());
            }
        }

        errorMessages.addAll(invalidOrders);
    }

    /**
     * 处理酒店订单数据
     */
    private void processHotelOrders(List<DdHotelOrderImportDTO> orders,
                                    List<String> errorMessages,
                                    AtomicInteger successCount,
                                    AtomicInteger failCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdHotelOrder> validEntities = new ArrayList<>();
        List<DdHotelOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            orderNoSet.add(order.getOrderNo());
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getCheckinEmployeeNo())) {
                workCodeSet.add(order.getCheckinEmployeeNo());
            }
        });
        // 查询已存在的酒店订单
        Map<String,DdHotelOrder> hotelOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getHotelOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdHotelOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));
        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdHotelOrderImportDTO order : orders) {
            try {
                // 数据校验
                String errorMsg = validateHotelOrder(order,projectInfoMap,deptIdMapByName,rosterMap,companyMap);
                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：" + errorMsg);
                    failCount.incrementAndGet();
                    continue;
                }

                // 转换为实体
                DdHotelOrder entity = convertToHotelOrder(order);
                if (hotelOrderMap.containsKey(order.getOrderNo())) {
                    DdHotelOrder hotelOrder = hotelOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(hotelOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("机票订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(entity);
                } else {
                    validEntities.add(entity);
                }
                
            } catch (Exception e) {
                log.error("处理酒店订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
                failCount.incrementAndGet();
            }
        }

        // 批量插入有效数据
        if (!validEntities.isEmpty()) {
            try {
                int insertCount = ddOrderMapper.batchInsertHotelOrders(validEntities);
                int updateCount = ddOrderMapper.batchUpdateHotelOrder(updateEntities);
                successCount.addAndGet(insertCount + updateCount);
            } catch (Exception e) {
                log.error("批量新增/更新酒店订单失败", e);
                invalidOrders.add("酒店订单批量处理失败，请联系管理员");
                failCount.addAndGet(validEntities.size() + updateEntities.size());
            }
        }

        errorMessages.addAll(invalidOrders);
    }

    /**
     * 处理用车订单数据
     */
    private void processVehicleOrders(List<DdVehicleOrderImportDTO> orders,
                                      List<String> errorMessages,
                                      AtomicInteger successCount,
                                      AtomicInteger failCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdVehicleOrder> validEntities = new ArrayList<>();
        List<DdVehicleOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            orderNoSet.add(order.getOrderNo());
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getPassengerEmployeeNo())) {
                workCodeSet.add(order.getPassengerEmployeeNo());
            }
        });
        // 查询已存在的酒店订单
        Map<String, DdVehicleOrder> vehicleOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getVehicleOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdVehicleOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));

        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdVehicleOrderImportDTO order : orders) {
            try {
                // 数据校验
                String errorMsg = validateVehicleOrder(order,projectInfoMap,deptIdMapByName,rosterMap,companyMap);
                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("用车订单[" + order.getOrderNo() + "]：" + errorMsg);
                    failCount.incrementAndGet();
                    continue;
                }

                // 转换为实体
                DdVehicleOrder entity = convertToVehicleOrder(order);
                if (vehicleOrderMap.containsKey(order.getOrderNo())) {
                    DdVehicleOrder vehicleOrder = vehicleOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(vehicleOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("机票订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(entity);
                } else {
                    validEntities.add(entity);
                }

            } catch (Exception e) {
                log.error("处理用车订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("用车订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
                failCount.incrementAndGet();
            }
        }

        // 批量插入有效数据
        if (!validEntities.isEmpty()) {
            try {
                int insertCount = ddOrderMapper.batchInsertVehicleOrders(validEntities);
                int updateCount = ddOrderMapper.batchUpdateVehicleOrder(updateEntities);
                successCount.addAndGet(insertCount + updateCount);
            } catch (Exception e) {
                log.error("批量新增/更新用车订单失败", e);
                invalidOrders.add("用车订单批量处理失败，请联系管理员");
                failCount.addAndGet(validEntities.size() + updateEntities.size());
            }
        }

        errorMessages.addAll(invalidOrders);
    }

    /**
     * 校验机票订单数据
     */
    private String validateFlightTicketOrder(DdFlightTicketOrderImportDTO order,
                                             Map<String, ProjectInfo> projectInfoMap,
                                             Map<String, Long> deptIdMapByName,
                                             Map<String, Roster> rosterMap,
                                             Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getPassengerEmployeeNo())) {
            Roster roster = rosterMap.get(order.getPassengerEmployeeNo());
            if (roster == null) {
                return "乘机人工号不存在";
            }
            order.setPassengerName(roster.getAliasName());
            order.setPassengerId(roster.getDeptId());
            order.setPassengerDeptId(roster.getId());
        }
        if (!StringUtils.hasText(order.getPassengerName())) {
            return "乘机人姓名不能为空";
        }
        if (!StringUtils.hasText(order.getArrivalLocation())) {
            return "到达地不能为空";
        }
        if (order.getCompanyActualPayment() == null ) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        }else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }

        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);

        return null;
    }

    /**
     * 校验酒店订单数据
     */
    private String validateHotelOrder(DdHotelOrderImportDTO order,
                                      Map<String, ProjectInfo> projectInfoMap,
                                      Map<String, Long> deptIdMapByName,
                                      Map<String, Roster> rosterMap,
                                      Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getCheckinEmployeeNo())) {
            Roster roster = rosterMap.get(order.getCheckinEmployeeNo());
            if (roster == null) {
                return "入住人工号不存在";
            }
            order.setCheckinPersonName(roster.getAliasName());
            order.setCheckinDeptId(roster.getDeptId());
            order.setCheckinUserId(roster.getId());
        }

        if (!StringUtils.hasText(order.getCheckinPersonName())) {
            return "入住人姓名不能为空";
        }
        if (order.getCompanyActualPayment() == null ) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        }else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }

        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);
        return null;
    }

    /**
     * 校验用车订单数据
     */
    private String validateVehicleOrder(DdVehicleOrderImportDTO order,
                                        Map<String, ProjectInfo> projectInfoMap,
                                        Map<String, Long> deptIdMapByName,
                                        Map<String, Roster> rosterMap,
                                        Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getPassengerEmployeeNo())) {
            Roster roster = rosterMap.get(order.getPassengerEmployeeNo());
            if (roster == null) {
                return "乘车人工号不存在";
            }
            order.setPassengerName(roster.getAliasName());
            order.setPassengerId(roster.getDeptId());
            order.setPassengerDeptId(roster.getId());
        }
        if (!StringUtils.hasText(order.getPassengerName())) {
            return "乘车人姓名不能为空";
        }
        if (order.getCompanyActualPayment() == null) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        }else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }
        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);
        return null;
    }

    /**
     * 转换为机票订单实体
     */
    private static DdFlightTicketOrder convertToFlightTicketOrder(DdFlightTicketOrderImportDTO dto) {
        DdFlightTicketOrder entity = new DdFlightTicketOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setPassengerEmployeeNo(dto.getPassengerEmployeeNo());
        entity.setPassengerId(dto.getPassengerId());
        entity.setPassengerDeptId(dto.getPassengerDeptId());

        entity.setPassengerName(dto.getPassengerName());
        entity.setTicketNo(dto.getTicketNo());
        entity.setTicketStatus(dto.getTicketStatus());
        entity.setDepartureLocation(dto.getDepartureLocation());
        entity.setArrivalLocation(dto.getArrivalLocation());
        entity.setFlightNo(dto.getFlightNo());
        entity.setDepartureTime(dto.getDepartureTime());
        entity.setLandingTime(dto.getLandingTime());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingDeptId(dto.getBookingDeptId());

        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return BaseBuildEntityUtil.buildSave(entity);
    }

    /**
     * 转换为酒店订单实体
     */
    private static DdHotelOrder convertToHotelOrder(DdHotelOrderImportDTO dto) {
        DdHotelOrder entity = new DdHotelOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setCheckinEmployeeNo(dto.getCheckinEmployeeNo());
        entity.setCheckinUserId(dto.getCheckinUserId());
        entity.setCheckinDeptId(dto.getCheckinDeptId());
        entity.setCheckinPersonName(dto.getCheckinPersonName());
        entity.setCityName(dto.getCityName());
        entity.setHotelName(dto.getHotelName());
        entity.setRoomType(dto.getRoomType());
        entity.setCheckinTime(dto.getCheckinTime());
        entity.setCheckoutTime(dto.getCheckoutTime());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setNumberOfDays(dto.getNumberOfDays());
        entity.setNumberOfRooms(dto.getNumberOfRooms());
        entity.setRoomNights(dto.getRoomNights());
        entity.setUnitPrice(dto.getUnitPrice());
        entity.setRoomStandardDifference(dto.getRoomStandardDifference());
        entity.setOrderStatus(dto.getOrderStatus());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingDeptId(dto.getBookingDeptId());

        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return BaseBuildEntityUtil.buildSave(entity);
    }

    /**
     * 转换为用车订单实体
     */
    private static DdVehicleOrder convertToVehicleOrder(DdVehicleOrderImportDTO dto) {
        DdVehicleOrder entity = new DdVehicleOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setPassengerEmployeeNo(dto.getPassengerEmployeeNo());
        entity.setPassengerId(dto.getPassengerId());
        entity.setPassengerDeptId(dto.getPassengerDeptId());
        entity.setPassengerName(dto.getPassengerName());
        entity.setTravelTime(dto.getTravelTime());
        entity.setArrivalTime(dto.getArrivalTime());
        entity.setVehicleType(dto.getVehicleType());
        entity.setDepartureCity(dto.getDepartureCity());
        entity.setDepartureAddress(dto.getDepartureAddress());
        entity.setArrivalCity(dto.getArrivalCity());
        entity.setArrivalAddress(dto.getArrivalAddress());
        entity.setTravelDistance(dto.getTravelDistance());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setPaymentType(dto.getPaymentType());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingDeptId(dto.getBookingDeptId());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return BaseBuildEntityUtil.buildSave(entity);
    }

    /**
     * 解析成本中心/所属项目
     */
    private String parseCostCenterProject(String costCenterProject,
                                        Object entity,
                                        Map<String, ProjectInfo> projectInfoMap,
                                        Map<String, Long> deptIdMapByName) {
        if (!StringUtils.hasText(costCenterProject)) {
            return null;
        }
        if (costCenterProject.startsWith("部门费用-")) {
            // 部门费用
            String deptName = costCenterProject.substring("部门费用-".length());
            if (!deptIdMapByName.containsKey(deptName)) {
                return "成本中心/所属项目-该部门不存在";
            }
            Long deptId = deptIdMapByName.get(deptName);
            if (entity instanceof DdFlightTicketOrderImportDTO) {
                DdFlightTicketOrderImportDTO order = (DdFlightTicketOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            } else if (entity instanceof DdHotelOrderImportDTO) {
                DdHotelOrderImportDTO order = (DdHotelOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            } else if (entity instanceof DdVehicleOrderImportDTO) {
                DdVehicleOrderImportDTO order = (DdVehicleOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            }
        } else {
            if (!projectInfoMap.containsKey(costCenterProject)) {
                return "成本中心/所属项目-该项目不存在";
            }
            ProjectInfo projectInfo = projectInfoMap.get(costCenterProject);
            // 项目费用
            if (entity instanceof DdFlightTicketOrderImportDTO) {
                DdFlightTicketOrderImportDTO order = (DdFlightTicketOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            } else if (entity instanceof DdHotelOrderImportDTO) {
                DdHotelOrderImportDTO order = (DdHotelOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            } else if (entity instanceof DdVehicleOrderImportDTO) {
                DdVehicleOrderImportDTO order = (DdVehicleOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            }
        }
        return null;
    }

    /**
     * 调用OA接口创建报销单
     */
    private Long callOACreateReimbursement(OaAccountVO oaAccountVO, Integer oaFormType, List<DdCommonOrder> orders, Long companyId, Long deptProjectId, Map<Long, ProjectAccountVO> projectAccountMap, Map<Long, List<OaXmysbDTO>> xmysbMap, Map<Long, List<OaBmyysbDTO>> bmyysbMap) throws JsonProcessingException {
        // 根据OA表单类型获取对应的枚举
        OAFormTypeEnum formTypeEnum = OAFormTypeEnum.getBusinessTypeEnum(oaFormType);
        if (formTypeEnum == null) {
            log.error("未找到对应的OA表单类型：{}", oaFormType);
            return null;
        }

        // 预算校验
        String budgetValidationResult = validateBudget(formTypeEnum, orders, deptProjectId, xmysbMap, bmyysbMap);
        if (budgetValidationResult != null) {
            log.error("预算校验失败：{}", budgetValidationResult);
            // 标记订单为预算不足状态，但不在这里更新数据库，由外部批量处理
            for (DdCommonOrder order : orders) {
                order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
            }
            return null;
        }

        // 构建OA请求DTO
        OaCreateRequestDTO oaCreateRequestDTO = new OaCreateRequestDTO();
        oaCreateRequestDTO.setWorkflowId(dbApiUtil.getWorkflowIdByName(formTypeEnum.getName()));
        oaCreateRequestDTO.setOaAccountVO(oaAccountVO);

        // 构建主表数据
        List<OaMainParamDTO> mainDataList = buildMainData(oaAccountVO, orders, companyId, deptProjectId, formTypeEnum, projectAccountMap, xmysbMap, bmyysbMap);
        oaCreateRequestDTO.setMainData(mainDataList);

        // 构建明细表数据
        List<Map<String, Object>> detailDataList = buildDetailData(orders, formTypeEnum);
        oaCreateRequestDTO.setDetailData(detailDataList);

        // 设置其他参数
        Map<String, Object> otherParams = new HashMap<>();
        otherParams.put("isnextflow", "0");
        oaCreateRequestDTO.setOtherParams(otherParams);

        // 设置请求名称
        String requestName = buildRequestName(formTypeEnum, orders);
        oaCreateRequestDTO.setRequestName(requestName);

        // 获取token
        String token = oaUtil.getToken();
        if (StrUtil.isBlank(token)) {
            log.error("获取OA token失败");
            return null;
        }

        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);

        // 序列化参数
        ObjectMapper objectMapper = new ObjectMapper();
        String mainParamStr = objectMapper.writeValueAsString(mainDataList);
        String otherParamsStr = JSONUtil.toJsonStr(otherParams);
        String detailParamStr = objectMapper.writeValueAsString(detailDataList);

        log.info("调用OA接口创建报销单，参数：workflowId={}, requestName={}", oaCreateRequestDTO.getWorkflowId(), requestName);

        // 调用OA接口
        JSONObject result = oaClient.doCreateRequest(token, appId, encryptUserId, url,
                oaCreateRequestDTO.getWorkflowId().toString(),
                requestName,
                mainParamStr,
                otherParamsStr,
                detailParamStr);

        if (!"SUCCESS".equals(result.getString("code"))) {
            log.error("调用OA接口失败，返回结果：{}", result);
            return null;
        }

        // 获取请求ID
        JSONObject data = result.getJSONObject("data");
        Long requestId = data.getLong("requestid");

        log.info("成功创建OA报销单，请求ID：{}", requestId);
        return requestId;
    }

    /**
     * 构建主表数据
     */
    private List<OaMainParamDTO> buildMainData(OaAccountVO oaAccountVO, List<DdCommonOrder> orders, Long companyId, Long deptProjectId, OAFormTypeEnum formTypeEnum,
                                               Map<Long, ProjectAccountVO> projectAccountMap, Map<Long, List<OaXmysbDTO>> xmysbMap, Map<Long, List<OaBmyysbDTO>> bmyysbMap) {
        List<OaMainParamDTO> mainDataList = new ArrayList<>();
        //流程信息
        mainDataList.add(buildMainParam("ApplicantID", String.valueOf(oaAccountVO.getOaId())));
        mainDataList.add(buildMainParam("bh", String.valueOf(oaAccountVO.getWorkcode())));
        mainDataList.add(buildMainParam("ApplicantDeptID", oaAccountVO.getDepartmentid()));
        mainDataList.add(buildMainParam("ApplicantTel", oaAccountVO.getMobile()));
        mainDataList.add(buildMainParam("ApplicantJobID", oaAccountVO.getJobtitle()));
        //收款方信息
        mainDataList.add(buildMainParam("dfmc", "滴滴出行科技有限公司")); //对方名称
        mainDataList.add(buildMainParam("khx", "招商银行股份有限公司北京东三环支行"));  //开户行
        mainDataList.add(buildMainParam("yxzh", "1229059399101080000208755"));  //银行账号
        mainDataList.add(buildMainParam("RelateAttachment", oaAccountVO.getMobile()));  //相关附件

        //计算orders合计金额
        String totalAmount = orders.stream().map(DdCommonOrder::getCompanyActualPayment).reduce(BigDecimal.ZERO, BigDecimal::add).toString();

        // 根据表单类型添加特定字段
        if (formTypeEnum == OAFormTypeEnum.FYBXD) {
            // CW-03 费用报销单特定字段
            mainDataList.add(buildMainParam("zffs", "1"));  //支付方式 默认值：转账
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属 公司
            mainDataList.add(buildMainParam("djzs", "1"));  //单据张数 默认值：1
            mainDataList.add(buildMainParam("zje", totalAmount));  //本次报销总金额
            mainDataList.add(buildMainParam("fkje", totalAmount)); //总付款金额
            mainDataList.add(buildMainParam("ysyjbm", String.valueOf(deptProjectId)));  //预算一级部门

        } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
            // XM-28 商机项目费用报销特定字段
            ProjectAccountVO projectAccountVO = projectAccountMap.get(deptProjectId);
            mainDataList.add(buildMainParam("pjname", String.valueOf(deptProjectId)));  //项目名称
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属公司
            mainDataList.add(buildMainParam("xmzt", projectAccountVO.getXmzt()));  //项目状态
            mainDataList.add(buildMainParam("bxr", String.valueOf(oaAccountVO.getOaId())));  //报销人
            mainDataList.add(buildMainParam("jsleixing", projectAccountVO.getJslxbb()));  //技术类型
            mainDataList.add(buildMainParam("bxrq", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));  //报销日期
            mainDataList.add(buildMainParam("khjl", projectAccountVO.getXmxsry()));  //客户经理
            mainDataList.add(buildMainParam("xmbm", projectAccountVO.getProjectNumber()));  //项目编码
            mainDataList.add(buildMainParam("ywgsbm", projectAccountVO.getYwgsbm()));  //业务归属部门(一级部门)
            mainDataList.add(buildMainParam("ywgsbmejbm", projectAccountVO.getYwgsbmejbm()));  //业务归属部门(二级部门)
            mainDataList.add(buildMainParam("bxlx", "2")); //报销类型 默认值：已到票付款
            mainDataList.add(buildMainParam("khmc", projectAccountVO.getZzkh()));  //客户名称
            mainDataList.add(buildMainParam("jehj", totalAmount));  //本次报销金额
            mainDataList.add(buildMainParam("fkje", totalAmount));  //付款总金额

        } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
            // XM-15 在建项目费用报销特定字段
            ProjectAccountVO projectAccountVO = projectAccountMap.get(deptProjectId);
            mainDataList.add(buildMainParam("xmmci", String.valueOf(deptProjectId)));  //项目名称
            mainDataList.add(buildMainParam("xmbm", projectAccountVO.getProjectName()));  //项目编码
            mainDataList.add(buildMainParam("xmzt", projectAccountVO.getXmzt()));  //项目状态
            mainDataList.add(buildMainParam("jslx", projectAccountVO.getJslxbb()));  //技术类型
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属公司
            mainDataList.add(buildMainParam("bxr", String.valueOf(oaAccountVO.getOaId())));  //报销人
            mainDataList.add(buildMainParam("bxrq", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));  //报销日期
            mainDataList.add(buildMainParam("xsry", projectAccountVO.getXmxsry()));  //客户经理
            mainDataList.add(buildMainParam("xmjl", projectAccountVO.getXmjl()));  //项目经理
            mainDataList.add(buildMainParam("xmfzr", projectAccountVO.getXmfzr()));  //交付负责人
            mainDataList.add(buildMainParam("ywgsbm", projectAccountVO.getYwgsbm()));  //业务归属部门(一级部门)
            mainDataList.add(buildMainParam("ywgsbmejbm", projectAccountVO.getYwgsbmejbm()));  //业务归属部门(二级部门)
            mainDataList.add(buildMainParam("xmgsbmejbm", projectAccountVO.getXmgsbmejbm()));  //项目归属部门(二级部门)
            mainDataList.add(buildMainParam("jehj", totalAmount));  //本次报销金额
            mainDataList.add(buildMainParam("fkje", totalAmount));  //付款总金额
            mainDataList.add(buildMainParam("fylx", "6")); //费用类型 默认值：项目费用
            mainDataList.add(buildMainParam("bxlx", "0")); //报销类型 默认值：已到票付款
            mainDataList.add(buildMainParam("skflb", "0"));  //收款方类别 默认值：供应商

        }
        return mainDataList;
    }

    /**
     * 构建主表参数DTO
     */
    private OaMainParamDTO buildMainParam(String fieldName, String fieldValue) {
        return OaMainParamDTO.builder()
                .fieldName(fieldName)
                .fieldValue(fieldValue)
                .build();
    }

    /**
     * 构建明细表数据
     */
    private List<Map<String, Object>> buildDetailData(List<DdCommonOrder> orders, OAFormTypeEnum formTypeEnum) {
        List<Map<String, Object>> detailDataList = new ArrayList<>();

        // 根据订单类型分组
        Map<OrderTypeEnum, List<DdCommonOrder>> ordersByType = orders.stream()
                .collect(Collectors.groupingBy(this::getOrderType));

        for (Map.Entry<OrderTypeEnum, List<DdCommonOrder>> entry : ordersByType.entrySet()) {
            OrderTypeEnum orderType = entry.getKey();
            List<DdCommonOrder> typeOrders = entry.getValue();

            // 计算该类型订单的总金额
            BigDecimal typeTotalAmount = calculateTotalAmount(typeOrders);

            Map<String, Object> detailDataMap = new LinkedHashMap<>();

            // 根据不同的表单类型添加特定字段
            if (formTypeEnum == OAFormTypeEnum.FYBXD) {
                // CW-03费用报销单特定字段 (formtable_main_125_dt1)
                detailDataMap.put("fyx", orderType.getName()); // 费用项
                detailDataMap.put("fyxlx", orderType.getExpenseType()); // 费用项类型
                detailDataMap.put("je", typeTotalAmount.toString()); // 付款金额
                detailDataMap.put("fyzy", buildExpenseSummary(typeOrders)); // 费用摘要
                detailDataMap.put("ysbm", getBudgetDept(orders)); // 预算部门
                detailDataMap.put("ysyjbm", getBudgetDept(orders)); // 预算一级部门
                detailDataMap.put("bxje", typeTotalAmount.toString()); // 报销金额
                detailDataMap.put("cxje", "0"); // 冲销金额
                detailDataMap.put("fyxdm", orderType.getCode()); // 费用项代码
                detailDataMap.put("fyxid", orderType.ordinal()); // 费用项ID

            } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
                // XM-28商机项目费用报销特定字段 (formtable_main_304_dt1)
                detailDataMap.put("fyx", orderType.getName()); // 费用项
                detailDataMap.put("fyxlb", orderType.getExpenseType()); // 费用项类别
                detailDataMap.put("je", typeTotalAmount.toString()); // 付款金额
                detailDataMap.put("memo", buildExpenseSummary(typeOrders)); // 说明
                detailDataMap.put("bxje", typeTotalAmount.toString()); // 报销金额
                detailDataMap.put("cxje", "0"); // 冲销金额
                detailDataMap.put("fyxdm", orderType.getCode()); // 费用项代码
                detailDataMap.put("fyxid", orderType.ordinal()); // 费用项ID
                detailDataMap.put("kmdm", getAccountCode(orderType)); // 科目代码
                detailDataMap.put("kmmc", getAccountName(orderType)); // 科目名称
                detailDataMap.put("xmys", "0"); // 项目预算
                detailDataMap.put("xmysbm", ""); // 项目预算编码
                detailDataMap.put("kyysje", "0"); // 可用预算金额
                detailDataMap.put("sfkhcd", "0"); // 是否客户承担
                detailDataMap.put("sl", "0"); // 税率

            } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
                // XM-15在建项目费用报销特定字段 (formtable_main_74_dt1)
                detailDataMap.put("fyx", orderType.getName()); // 费用项
                detailDataMap.put("fyxlb", orderType.getExpenseType()); // 费用项类别
                detailDataMap.put("je", typeTotalAmount.toString()); // 付款金额
                detailDataMap.put("memo", buildExpenseSummary(typeOrders)); // 说明
                detailDataMap.put("bxje", typeTotalAmount.toString()); // 报销金额
                detailDataMap.put("cxje", "0"); // 冲销金额
                detailDataMap.put("fyxdm", orderType.getCode()); // 费用项代码
                detailDataMap.put("fyxid", orderType.ordinal()); // 费用项ID
                detailDataMap.put("kmdm", getAccountCode(orderType)); // 科目代码
                detailDataMap.put("kmmc", getAccountName(orderType)); // 科目名称
                detailDataMap.put("xmysbm", ""); // 项目预算编码
                detailDataMap.put("kyysje", "0"); // 可用预算金额
                detailDataMap.put("yfsjelj", "0"); // 原发生金额累计
                detailDataMap.put("bxhkyysje", "0"); // 报销后可用预算金额
                detailDataMap.put("sfkhcd", "0"); // 是否客户承担
                detailDataMap.put("mxid", ""); // 明细ID
            }

            detailDataList.add(detailDataMap);
        }

        return detailDataList;
    }

    /**
     * 获取科目代码
     */
    private String getAccountCode(OrderTypeEnum orderType) {
        switch (orderType) {
            case FLIGHT_TICKET:
                return "6601"; // 机票费用科目代码
            case HOTEL:
                return "6602"; // 住宿费用科目代码
            case VEHICLE:
                return "6603"; // 交通费用科目代码
            default:
                return "6601";
        }
    }

    /**
     * 获取科目名称
     */
    private String getAccountName(OrderTypeEnum orderType) {
        switch (orderType) {
            case FLIGHT_TICKET:
                return "机票费用";
            case HOTEL:
                return "住宿费用";
            case VEHICLE:
                return "交通费用";
            default:
                return "机票费用";
        }
    }

    /**
     * 构建请求名称
     */
    private String buildRequestName(OAFormTypeEnum formTypeEnum, List<DdCommonOrder> orders) {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String companyName = getCompanyName(orders);
        return String.format("%s-%s-%s", formTypeEnum.getName(), companyName, currentDate);
    }

    /**
     * 计算总金额
     */
    private BigDecimal calculateTotalAmount(List<DdCommonOrder> orders) {
        return orders.stream()
                .map(this::getOrderAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取订单金额
     */
    private BigDecimal getOrderAmount(DdCommonOrder order) {
        return order.getCompanyActualPayment() != null ? order.getCompanyActualPayment() : BigDecimal.ZERO;
    }

    /**
     * 获取订单类型
     */
    private OrderTypeEnum getOrderType(DdCommonOrder order) {
        return order.getOrderType();
    }

    /**
     * 构建费用摘要
     */
    private String buildExpenseSummary(List<DdCommonOrder> orders) {
        return orders.stream()
                .map(this::getOrderSummary)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining("; "));
    }

    /**
     * 获取订单摘要
     */
    private String getOrderSummary(DdCommonOrder order) {
        // 由于DdCommonOrder没有具体的订单详情字段，这里返回订单类型和编号
        return String.format("%s-%s", order.getOrderType().getName(), order.getOrderNo());
    }

    /**
     * 构建项目报销说明
     */
    private String buildProjectExpenseSummary(List<DdCommonOrder> orders) {
        return buildExpenseSummary(orders);
    }

    /**
     * 获取预算部门
     */
    private String getBudgetDept(List<DdCommonOrder> orders) {
        if (!orders.isEmpty()) {
            return orders.get(0).getBookingDeptName();
        }
        return "";
    }

    /**
     * 获取项目名称
     */
    private String getProjectName(List<DdCommonOrder> orders) {
        if (!orders.isEmpty()) {
            return orders.get(0).getProjectName();
        }
        return "";
    }

    /**
     * 获取公司名称
     */
    private String getCompanyName(List<DdCommonOrder> orders) {
        if (!orders.isEmpty()) {
            return orders.get(0).getCompanyName();
        }
        return "";
    }

    /**
     * 更新订单的报销状态
     */
    private void updateOrderReimbursementStatus(List<DdCommonOrder> orders, Long requestId, BusinessOrderReimbursedStatusEnum status) {
        List<DdFlightTicketOrder> flightOrdersToUpdate = new ArrayList<>();
        List<DdHotelOrder> hotelOrdersToUpdate = new ArrayList<>();
        List<DdVehicleOrder> vehicleOrdersToUpdate = new ArrayList<>();

        for (DdCommonOrder commonOrder : orders) {
            if (commonOrder.getOrderType() == OrderTypeEnum.FLIGHT_TICKET) {
                DdFlightTicketOrder flightOrder = new DdFlightTicketOrder();
                flightOrder.setId(commonOrder.getId());
                flightOrder.setInitiationStatus(status.getValue());
                flightOrder.setInitiationDate(LocalDate.now());
                if (requestId != null || commonOrder.getRequestId() != null) {
                    flightOrder.setRequestId(requestId != null ? requestId : commonOrder.getRequestId());
                }
                flightOrdersToUpdate.add(flightOrder);
            } else if (commonOrder.getOrderType() == OrderTypeEnum.HOTEL) {
                DdHotelOrder hotelOrder = new DdHotelOrder();
                hotelOrder.setId(commonOrder.getId());
                hotelOrder.setInitiationStatus(status.getValue());
                hotelOrder.setInitiationDate(LocalDate.now());
                if (requestId != null || commonOrder.getRequestId() != null) {
                    hotelOrder.setRequestId(requestId != null ? requestId : commonOrder.getRequestId());
                }
                hotelOrdersToUpdate.add(hotelOrder);
            } else if (commonOrder.getOrderType() == OrderTypeEnum.VEHICLE) {
                DdVehicleOrder vehicleOrder = new DdVehicleOrder();
                vehicleOrder.setId(commonOrder.getId());
                vehicleOrder.setInitiationStatus(status.getValue());
                vehicleOrder.setInitiationDate(LocalDate.now());
                if (requestId != null || commonOrder.getRequestId() != null) {
                    vehicleOrder.setRequestId(requestId != null ? requestId : commonOrder.getRequestId());
                }
                vehicleOrdersToUpdate.add(vehicleOrder);
            }
        }

        if (!flightOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateFlightTicketOrder(flightOrdersToUpdate);
        }
        if (!hotelOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateHotelOrder(hotelOrdersToUpdate);
        }
        if (!vehicleOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateVehicleOrder(vehicleOrdersToUpdate);
        }
    }

}