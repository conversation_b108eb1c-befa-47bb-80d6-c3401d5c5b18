package com.gok.pboot.pms.didi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.didi.client.DidiClient;
import com.gok.pboot.pms.didi.dto.*;
import com.gok.pboot.pms.didi.service.DidiProjectSyncService;
import com.gok.pboot.pms.didi.util.DidiSignUtil;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.service.IProjectInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 滴滴项目同步服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@Service
public class DidiProjectSyncServiceImpl implements DidiProjectSyncService {

    @Resource
    private DidiClient didiClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IProjectInfoService projectInfoService;

    @Value("${didi.client.id:1}")
    public String clientId;

    @Value("${didi.client.secret:1}")
    public String clientSecret;

    @Value("${didi.client.grant.type:client_credentials}")
    private String grantType;

    private static final String DIDI_ACCESS_TOKEN_KEY = "didi:access_token";

    private static final String DIDI_ACCESS_AUTH_REQ_KEY = "didi:auth_req";


    public synchronized boolean syncProjectToDidi(ProjectInfo projectInfo) {
        try {
            // 转换项目信息
            DidiProjectSyncReq syncReq = convertToDidiProjectSyncReq(projectInfo);

            // 调用滴滴API同步项目
            DidiResult<DidiProjectSyncRes> result = didiClient.addProject(syncReq);

            if (result != null && result.getErrno() == 0) {
                log.info("项目同步成功: {}", projectInfo.getItemNo());
                return true;
            } else {
                log.error("项目同步失败: {}, 错误信息: {}", projectInfo.getItemNo(), 
                    result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("项目同步异常: {}", projectInfo.getItemNo(), e);
            return false;
        }
    }



    @Override
    public void batchSyncProjects() {
        List<ProjectInfo> projectInfoList = projectInfoService.list();
        for (ProjectInfo projectInfo : projectInfoList) {
            boolean success = syncProjectToDidi(projectInfo);
            if (!success) {
                log.error("批量同步中项目同步失败: {}", projectInfo.getItemNo());
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}", projectInfoList.size(), 
            projectInfoList.stream().mapToInt(p -> syncProjectToDidi(p) ? 1 : 0).sum());

    }
    @Override
    public DidiAuthReq getDidiAuthReq() {
        String str = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_AUTH_REQ_KEY);
        if (str == null) {
            return null;
        }
        return JSONObject.parseObject(str, DidiAuthReq.class);
    }


    /**
     * 获取滴滴访问令牌
     */
    @Override
    public synchronized String getAccessToken() {
        try {
            String accessToken = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_TOKEN_KEY);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            DidiAuthReq authReq = new DidiAuthReq();
            authReq.setClient_id(clientId);
            authReq.setClient_secret(clientSecret);
            authReq.setGrant_type(grantType);
            authReq.setTimestamp((int) (System.currentTimeMillis() / 1000));
            // 这里需要根据滴滴的签名算法生成签名
            authReq.setSign(generateSign(authReq));

            DidiAuthRes authRes = didiClient.getToken(authReq);
            if (authRes != null && authRes.getAccess_token() != null) {
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_TOKEN_KEY, authRes.getAccess_token(), 25, TimeUnit.MINUTES);
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_AUTH_REQ_KEY, JSONObject.toJSONString(authReq), 25, TimeUnit.MINUTES);
                return authRes.getAccess_token();
            }
        } catch (Exception e) {
            log.error("获取滴滴访问令牌异常", e);
            stringRedisTemplate.delete(DIDI_ACCESS_TOKEN_KEY);
            stringRedisTemplate.delete(DIDI_ACCESS_AUTH_REQ_KEY);
            throw new BusinessException("获取滴滴访问令牌异常");
        }
        return null;
    }

    /**
     * 生成签名
     * 注意：这里需要根据滴滴的具体签名算法实现
     */
    private String generateSign(DidiAuthReq authReq) {
        return DidiSignUtil.generateSign(authReq);
    }

    /**
     * 将项目信息转换为滴滴项目同步请求
     */
    private DidiProjectSyncReq convertToDidiProjectSyncReq(ProjectInfo projectInfo) {

    //同步【项目名称】、【项目编号】、【项目经理】
        return new DidiProjectSyncReq()
                .setType(1)
                .setName(projectInfo.getItemName())
                .setOut_budget_id(projectInfo.getItemNo())
                .setLeader_id("")
                ;
    }
} 