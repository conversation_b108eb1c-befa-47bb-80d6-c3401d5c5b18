package com.gok.pboot.pms.didi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.didi.client.DidiClient;
import com.gok.pboot.pms.didi.dto.*;
import com.gok.pboot.pms.didi.service.DidiProjectSyncService;
import com.gok.pboot.pms.didi.util.DidiSignUtil;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.service.IProjectInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 滴滴项目同步服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@Service
public class DidiProjectSyncServiceImpl implements DidiProjectSyncService {

    @Resource
    private DidiClient didiClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IProjectInfoService projectInfoService;

    @Value("${didi.client.id:1}")
    public String clientId;

    @Value("${didi.client.secret:1}")
    public String clientSecret;

    @Value("${didi.client.grant.type:client_credentials}")
    private String grantType;

    private static final String DIDI_ACCESS_TOKEN_KEY = "didi:access_token";

    private static final String DIDI_ACCESS_AUTH_REQ_KEY = "didi:auth_req";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public synchronized boolean syncProjectToDidi(ProjectInfo projectInfo) {
        try {
            // 转换项目信息
            DidiProjectSyncReq syncReq = convertToDidiProjectSyncReq(projectInfo);

            // 调用滴滴API同步项目
            DidiResult<DidiProjectSyncRes> result = didiClient.addProject(syncReq);

            if (result != null && result.getErrno() == 0) {
                log.info("项目同步成功: {}", projectInfo.getItemNo());
                return true;
            } else {
                log.error("项目同步失败: {}, 错误信息: {}", projectInfo.getItemNo(), 
                    result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("项目同步异常: {}", projectInfo.getItemNo(), e);
            return false;
        }
    }



    @Override
    public void batchSyncProjects() {
        List<ProjectInfo> projectInfoList = projectInfoService.list();
        for (ProjectInfo projectInfo : projectInfoList) {
            boolean success = syncProjectToDidi(projectInfo);
            if (!success) {
                log.error("批量同步中项目同步失败: {}", projectInfo.getItemNo());
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}", projectInfoList.size(), 
            projectInfoList.stream().mapToInt(p -> syncProjectToDidi(p) ? 1 : 0).sum());

    }
    @Override
    public DidiAuthReq getDidiAuthReq() {
        String str = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_AUTH_REQ_KEY);
        if (str == null) {
            return null;
        }
        return JSONObject.parseObject(str, DidiAuthReq.class);
    }


    /**
     * 获取滴滴访问令牌
     */
    @Override
    public String getAccessToken() {
        try {
            String accessToken = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_TOKEN_KEY);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            DidiAuthReq authReq = new DidiAuthReq();
            authReq.setClient_id(clientId);
            authReq.setClient_secret(clientSecret);
            authReq.setGrant_type(grantType);
            authReq.setTimestamp((int) (System.currentTimeMillis() / 1000));
            // 这里需要根据滴滴的签名算法生成签名
            authReq.setSign(generateSign(authReq));

            DidiAuthRes authRes = didiClient.getToken(authReq);
            if (authRes != null && authRes.getAccess_token() != null) {
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_TOKEN_KEY, authRes.getAccess_token(), 25, TimeUnit.MINUTES);
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_AUTH_REQ_KEY, JSONObject.toJSONString(authReq), 25, TimeUnit.MINUTES);
                return authRes.getAccess_token();
            }
        } catch (Exception e) {
            log.error("获取滴滴访问令牌异常", e);
            stringRedisTemplate.delete(DIDI_ACCESS_TOKEN_KEY);
            stringRedisTemplate.delete(DIDI_ACCESS_AUTH_REQ_KEY);
            throw new BusinessException("获取滴滴访问令牌异常");
        }
        return null;
    }

    /**
     * 生成签名
     * 注意：这里需要根据滴滴的具体签名算法实现
     */
    private String generateSign(DidiAuthReq authReq) {
        return DidiSignUtil.generateSign(authReq);
    }

    /**
     * 将项目信息转换为滴滴项目同步请求
     */
    private DidiProjectSyncReq convertToDidiProjectSyncReq(ProjectInfo projectInfo) {
        DidiProjectSyncReq syncReq = new DidiProjectSyncReq();
//
//        // 基本信息
//        syncReq.setProject_code(projectInfo.getItemNo());
//        syncReq.setProject_name(projectInfo.getItemName());
//        syncReq.setProject_status(convertProjectStatus(projectInfo.getProjectStatus()));
//
//        // 时间信息
//        if (projectInfo.getProjectDate() != null) {
//            syncReq.setStart_date(projectInfo.getProjectDate());
//        }
//        if (projectInfo.getProjectFilingTime() != null) {
//            syncReq.setEnd_date(projectInfo.getProjectFilingTime().format(DATE_FORMATTER));
//        }
//        if (projectInfo.getExpectedCompleteTime() != null) {
//            syncReq.setEnd_date(projectInfo.getExpectedCompleteTime());
//        }
//
//        // 预算信息
//        if (projectInfo.getProPackageBudget() != null) {
//            // 转换为分（滴滴要求金额单位为分）
//            syncReq.setBudget_amount(projectInfo.getProPackageBudget().multiply(new BigDecimal("100")).longValue());
//        }
//
//        // 负责人信息
//        syncReq.setManager_name(projectInfo.getManagerUserName());
//        // TODO: 需要从用户表获取手机号
//        // syncReq.setManager_phone(projectInfo.getManagerPhone());
//
//        // 项目描述
//        syncReq.setDescription(projectInfo.getProConstructionScope());
//
//        // 项目地址
//        syncReq.setProject_address(projectInfo.getProjectLocation());
//
//        // 客户信息
//        syncReq.setCustomer_name(projectInfo.getCustomerName());
//        syncReq.setCustomer_contact(projectInfo.getCustomerContact());
//        syncReq.setCustomer_phone(projectInfo.getCustomerPhone());
//
//        // 成本中心信息
//        if (projectInfo.getBusinessDepartment() != null) {
//            syncReq.setCost_center_name(projectInfo.getBusinessDepartment());
//            syncReq.setCost_center_code(projectInfo.getBusinessDepartmentId() != null ?
//                projectInfo.getBusinessDepartmentId().toString() : null);
//        }
//
//        // 备注
//        syncReq.setRemark(projectInfo.getRemark());
//
        return syncReq;
    }

    /**
     * 转换项目状态
     */
    private Integer convertProjectStatus(String projectStatus) {
        if (projectStatus == null) {
            // 默认进行中
            return 1;
        }
        
        // 根据实际项目状态枚举进行转换
        switch (projectStatus) {
            // 假设1表示进行中
            case "1":
                return 1;
            // 假设2表示已完成
            case "2":
                return 2;
            // 假设3表示已暂停
            case "3":
                return 3;
            // 假设4表示已取消
            case "4":
                return 4;
            default:
                // 默认进行中
                return 1;
        }
    }
} 