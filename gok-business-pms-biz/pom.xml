<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-pms</artifactId>
        <version>1.0.160</version>
    </parent>

    <artifactId>gok-business-pms-biz</artifactId>
    <packaging>jar</packaging>

    <description>业务一体化服务</description>


    <dependencies>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.27</version>
        </dependency>
        <!-- ojdbc8 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>********</version>
        </dependency>
        <!--PG-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!--mssql-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>8.4.1.jre8</version>
        </dependency>
        <!--数据操作权限-->
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
            <version>1.0.41</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.gok.components</groupId>-->
<!--            <artifactId>gok-components-excel</artifactId>-->
<!--            <version>1.0.41</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-excel-api</artifactId>
            <version>1.0.178</version>
        </dependency>
        <!--文件系统-->
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-file</artifactId>
            <version>1.0.41</version>
        </dependency>

        <!--日志处理-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-log</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-security</artifactId>
        </dependency>
        <!--路由配置 -->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-gateway</artifactId>
        </dependency>
        <!-- 企业微信 -->
        <dependency>
            <groupId>com.gok</groupId>
            <artifactId>wx-cp-spring-boot-starter</artifactId>
            <version>0.0.6</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!-- cas sdk -->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>3.6.4</version>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--AOP 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot3-starter</artifactId>
            <version>1.5.33</version>
        </dependency>

        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-ehr-api</artifactId>
            <version>1.0.170</version>
        </dependency>
        <!--中台消息推送 API-->
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-message-api</artifactId>
            <version>1.1.333</version>
        </dependency>
        <!--中台任务推送 API-->
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-task-api</artifactId>
            <version>1.1.262</version>
        </dependency>
        <!-- 数字财务 -->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-financial-api</artifactId>
            <version>1.0.159</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
		<!-- Mockito 测试框架 -->
      	<dependency>
        	<groupId>org.mockito</groupId>
        	<artifactId>mockito-core</artifactId>
        	<version>1.10.19</version>
        	<scope>test</scope>
    	</dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-upms-api</artifactId>
            <version>1.1.262</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-log-api</artifactId>
            <version>1.1.262</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-admin-api</artifactId>
            <version>1.1.313</version>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-file-biz</artifactId>
            <version>1.0.158</version>
        </dependency>
        <dependency>
            <groupId>com.gok.ehr</groupId>
            <artifactId>ehr-common-secret</artifactId>
            <version>0.0.9</version>
        </dependency>
    </dependencies>


    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <gok-env>dev</gok-env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <gok-env>test</gok-env>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <gok-env>pre</gok-env>
            </properties>
        </profile>
        <profile>
            <id>master</id>
            <properties>
                <gok-env>prod</gok-env>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.gok.pboot.pms.PmsServiceApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
